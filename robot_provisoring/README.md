# Provisioning dla Raspbiana 🍓

To repozytorium zawiera skrypty i konfiguracje do automatyzacji provisioningowania systemu opartego o Raspbian dla ARGUS - ROBOT.

## 📌 Cel

Automatyzacja konfiguracji:
- Nowych instalacji Raspbiana wersja Lite
- Raspberry Pi CORE dla Argus - ROBOT
## 🛠 Wymagania

- Raspberry Pi z Raspbian (Bookworm)
- Dostęp SSH (domyślnie włączony w nowszych wersjach)
- Podstawowe narzędzia: `curl`, `sudo`

## 📂 Struktura katalogów
### 🔍 Opis struktury

1. **files/** - zawiera gotowe pliki konfiguracyjne do wdrożenia:
2. **scripts/** - skrypty wykonawcze:
3. **network/** - konfiguracje WAN
4. **services/** - pliki SYSTEMD (service) systemu

Przykład użycia:
```bash
# Pełna instalacja
sudo ./install.sh


