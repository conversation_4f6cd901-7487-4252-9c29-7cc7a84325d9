#!/bin/bash

# <PERSON><PERSON><PERSON> sprawdzająca wersję Raspbiana
check_raspbian_version() {
    if [ ! -f /etc/os-release ]; then
        echo "Błąd: Nie znaleziono pliku /etc/os-release"
        return 1
    fi

    local os_id=$(grep '^ID=' /etc/os-release | cut -d '=' -f 2 | tr -d '"')
    local os_version=$(grep '^VERSION_ID=' /etc/os-release | cut -d '=' -f 2 | tr -d '"')
    local os_name=$(grep '^PRETTY_NAME=' /etc/os-release | cut -d '=' -f 2 | tr -d '"')

    if [ "$os_id" != "raspbian" ] && [ "$os_id" != "debian" ]; then
        echo "To nie jest Raspbian/Debian! To jest: $os_name"
        return 1
    fi

    if [ "$os_version" == "12" ] || echo "$os_name" | grep -qi "bookworm"; then
        echo "System to Raspbian Bookworm ($os_name)"
        return 0
    else
        echo "To nie jest Raspbian Bookworm. Obecna wersja: $os_name"
        return 1
    fi
}

main() {
    if check_raspbian_version; then
        echo "System to Raspbian Bookworm - wersja $(grep 'VERSION=' /etc/os-release | cut -d '"' -f 2)"
        echo "Zaczynam proces PROVISORING dla projektu ARGUS - ROBOT"
        echo "UWAGA!!!"
        echo "PODCZAS INSTALACJI NASTĄPI ROZŁACZENIE Z SIECIĄ WIFI"
        echo "ZALECANE PODŁĄCZENIE ZA POMOCĄ LAN W CELU POPRAWNOSCI CALEGO PROCESU"
        echo "NIE ZASTOSOWANIE LAN GROZI WYMOGIEM REINSTALL RASPBIAN I CALEGO PROCESU"
        exit 0
    else
        exit 0
    fi
}

main

