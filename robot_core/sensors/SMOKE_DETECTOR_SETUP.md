# Instalacja czujnika dymu MQ-2

## Wymagane komponenty
- Czujnik MQ-2 (detektor dymu i gazów)
- Konwerter ADC ADS1115 (16-bit I2C ADC)
- Rezystor 10kΩ
- Przewody połączeniowe

## Schemat połączeń

### MQ-2 → ADS1115
```
MQ-2 VCC  → 5V (zasilanie czujnika)
MQ-2 GND  → GND
MQ-2 AOUT → ADS1115 A0 (sygnał analogowy)
MQ-2 DOUT → (nie używane)
```

### ADS1115 → Raspberry Pi
```
ADS1115 VDD → 3.3V
ADS1115 GND → GND
ADS1115 SCL → GPIO 3 (I2C SCL)
ADS1115 SDA → GPIO 2 (I2C SDA)
ADS1115 ADDR → GND (adres 0x48)
ADS1115 ALRT → (nie używane)
```

## Konfiguracja I2C

1. Włącz I2C na Raspberry Pi:
```bash
sudo raspi-config
# Interface Options → I2C → Enable
```

2. Sprawdź czy urządzenia są wykryte:
```bash
sudo i2cdetect -y 1
# Powinieneś zobaczyć adres 0x48 dla ADS1115
```

## Kalibracja czujnika

⚠️ **WAŻNE**: Czujnik MQ-2 wymaga kalibracji w czystym powietrzu!

1. Umieść czujnik w czystym powietrzu (bez dymu/gazów)
2. Uruchom węzeł czujnika - automatycznie wykona kalibrację
3. Poczekaj 2-3 minuty na stabilizację

## Progi alarmowe (można dostosować w kodzie)
- **Dym**: 300 ppm
- **Gazy**: 1000 ppm

## Testowanie

```bash
# Uruchom węzeł czujnika
ros2 run sensors smoke

# W innym terminalu sprawdź topiki
ros2 topic list | grep smoke
ros2 topic echo /smoke/concentration
ros2 topic echo /smoke/alarm
```

## Rozwiązywanie problemów

### Błąd I2C
- Sprawdź połączenia SDA/SCL
- Upewnij się że I2C jest włączone
- Sprawdź adres urządzenia: `sudo i2cdetect -y 1`

### Nieprawidłowe odczyty
- Sprawdź zasilanie 5V dla MQ-2
- Poczekaj na rozgrzanie czujnika (2-3 minuty)
- Wykonaj ponowną kalibrację w czystym powietrzu

### Fałszywe alarmy
- Dostosuj progi w pliku `smoke.py`
- Sprawdź czy czujnik nie jest narażony na wilgoć
- Upewnij się że kalibracja została wykonana poprawnie
