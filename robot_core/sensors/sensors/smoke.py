#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
from std_msgs.msg import Float32, Bool
import smbus2
import time
import math

class SmokeDetectorNode(Node):
    def __init__(self):
        super().__init__('smoke_detector_node')
        
        # Konfiguracja I2C dla ADS1115 (16-bit ADC)
        self.i2c_bus = 1
        self.ads1115_addr = 0x48  # Domyślny adres ADS1115
        self.bus = smbus2.SMBus(self.i2c_bus)
        
        # Parametry czujnika MQ-2
        self.channel = 0  # Kanał A0 na ADS1115
        self.r_load = 10000  # Rezystor obciążenia (10kΩ)
        self.clean_air_ratio = 9.83  # Stosunek Rs/R0 w czystym powietrzu dla MQ-2
        self.smoke_curve = [2.3, 0.53, -0.44]  # Krzywa kalibracyjna dla dymu [x, y, slope]
        
        # Kalibracja R0 (rezystancja w czystym powietrzu)
        self.r0 = None
        self.calibration_samples = 50
        
        # Progi alarmowe
        self.smoke_threshold = 300  # ppm
        self.gas_threshold = 1000   # ppm
        
        # Inicjalizacja ADS1115
        self._init_ads1115()
        
        # Kalibracja czujnika
        self._calibrate_sensor()
        
        # Publishers
        self.smoke_pub = self.create_publisher(Float32, 'smoke/concentration', 10)
        self.gas_pub = self.create_publisher(Float32, 'smoke/gas_concentration', 10)
        self.alarm_pub = self.create_publisher(Bool, 'smoke/alarm', 10)
        
        # Timer do odczytu danych
        self.timer = self.create_timer(1.0, self.read_sensor)  # 1 Hz
        
        self.get_logger().info("Smoke Detector Node started")
        self.get_logger().info(f"R0 calibrated to: {self.r0:.2f} Ohm")

    def _init_ads1115(self):
        """Inicjalizacja ADS1115"""
        try:
            # Konfiguracja ADS1115:
            # - Single-ended A0
            # - ±4.096V range
            # - 128 SPS
            config = 0xC183
            self.bus.write_i2c_block_data(self.ads1115_addr, 0x01, 
                                        [(config >> 8) & 0xFF, config & 0xFF])
            time.sleep(0.1)
            self.get_logger().info("ADS1115 initialized")
        except Exception as e:
            self.get_logger().error(f"ADS1115 init failed: {str(e)}")
            raise

    def _read_adc(self):
        """Odczyt wartości ADC z ADS1115"""
        try:
            # Rozpocznij konwersję
            config = 0xC183 | 0x8000  # Start single conversion
            self.bus.write_i2c_block_data(self.ads1115_addr, 0x01,
                                        [(config >> 8) & 0xFF, config & 0xFF])
            time.sleep(0.1)
            
            # Odczytaj wynik
            data = self.bus.read_i2c_block_data(self.ads1115_addr, 0x00, 2)
            raw_adc = (data[0] << 8) | data[1]
            
            # Konwersja na napięcie (±4.096V, 16-bit)
            if raw_adc > 32767:
                raw_adc -= 65536
            voltage = raw_adc * 4.096 / 32767
            
            return max(0, voltage)  # Zapewnij nieujemne napięcie
            
        except Exception as e:
            self.get_logger().error(f"ADC read failed: {str(e)}")
            return 0

    def _calculate_resistance(self, voltage):
        """Oblicz rezystancję czujnika Rs"""
        if voltage <= 0:
            return float('inf')
        
        # Rs = (Vc - Vout) * RL / Vout
        # Zakładając Vc = 5V (napięcie zasilania czujnika)
        vc = 5.0
        if voltage >= vc:
            return 0
        
        rs = (vc - voltage) * self.r_load / voltage
        return rs

    def _calibrate_sensor(self):
        """Kalibracja czujnika w czystym powietrzu"""
        self.get_logger().info("Calibrating sensor in clean air...")
        
        rs_sum = 0
        valid_samples = 0
        
        for i in range(self.calibration_samples):
            voltage = self._read_adc()
            rs = self._calculate_resistance(voltage)
            
            if rs != float('inf') and rs > 0:
                rs_sum += rs
                valid_samples += 1
            
            time.sleep(0.1)
        
        if valid_samples > 0:
            rs_avg = rs_sum / valid_samples
            self.r0 = rs_avg / self.clean_air_ratio
        else:
            self.get_logger().error("Calibration failed - no valid samples")
            self.r0 = 10000  # Wartość domyślna
        
        self.get_logger().info(f"Calibration complete. R0 = {self.r0:.2f} Ohm")

    def _calculate_ppm(self, rs_r0_ratio, curve):
        """Oblicz stężenie w ppm na podstawie krzywej kalibracyjnej"""
        if rs_r0_ratio <= 0:
            return 0
        
        # Logarytmiczna interpolacja: ppm = a * (Rs/R0)^b
        return curve[0] * (rs_r0_ratio ** curve[2])

    def read_sensor(self):
        """Główna funkcja odczytu czujnika"""
        try:
            # Odczytaj napięcie
            voltage = self._read_adc()
            
            # Oblicz rezystancję
            rs = self._calculate_resistance(voltage)
            
            if self.r0 is None or self.r0 <= 0:
                self.get_logger().warn("Sensor not calibrated properly")
                return
            
            # Oblicz stosunek Rs/R0
            rs_r0_ratio = rs / self.r0
            
            # Oblicz stężenia
            smoke_ppm = self._calculate_ppm(rs_r0_ratio, self.smoke_curve)
            
            # Przybliżone stężenie gazów (uproszczona krzywa)
            gas_curve = [2.3, 0.53, -0.65]  # Inna krzywa dla gazów
            gas_ppm = self._calculate_ppm(rs_r0_ratio, gas_curve)
            
            # Publikuj dane
            self.smoke_pub.publish(Float32(data=float(smoke_ppm)))
            self.gas_pub.publish(Float32(data=float(gas_ppm)))
            
            # Sprawdź alarmy
            alarm_triggered = (smoke_ppm > self.smoke_threshold or 
                             gas_ppm > self.gas_threshold)
            self.alarm_pub.publish(Bool(data=alarm_triggered))
            
            # Logowanie
            if alarm_triggered:
                self.get_logger().warn(
                    f"ALARM! Smoke: {smoke_ppm:.1f}ppm, Gas: {gas_ppm:.1f}ppm"
                )
            else:
                self.get_logger().info(
                    f"Smoke: {smoke_ppm:.1f}ppm, Gas: {gas_ppm:.1f}ppm, "
                    f"Rs/R0: {rs_r0_ratio:.2f}",
                    throttle_duration_sec=5.0
                )
                
        except Exception as e:
            self.get_logger().error(f"Sensor read error: {str(e)}")

    def __del__(self):
        if hasattr(self, 'bus'):
            self.bus.close()

def main(args=None):
    rclpy.init(args=args)
    node = SmokeDetectorNode()
    
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        node.get_logger().info("Shutting down smoke detector...")
    finally:
        node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
